#!/usr/bin/env python3
"""
System Validation Framework
Prevents misassessment of system functionality by continuously validating actual capabilities
"""

import asyncio
import logging
import json
from datetime import datetime
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
from enum import Enum

class FunctionalityLevel(Enum):
    FULLY_FUNCTIONAL = "🟢 95%+"
    MOSTLY_FUNCTIONAL = "🟡 70-94%"
    PARTIALLY_FUNCTIONAL = "🟠 40-69%"
    MINIMAL_FUNCTIONAL = "🔴 10-39%"
    NON_FUNCTIONAL = "⚫ <10%"

@dataclass
class ComponentStatus:
    name: str
    file_path: str
    lines_of_code: int
    functionality_level: FunctionalityLevel
    working_features: List[str]
    broken_features: List[str]
    test_results: Dict[str, bool]
    last_validated: datetime

class SystemValidator:
    """Validates actual system functionality vs documented claims"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.components = {}
        
    async def validate_data_pipeline(self) -> ComponentStatus:
        """Validate data pipeline functionality"""
        working_features = []
        broken_features = []
        test_results = {}
        
        try:
            # Test Redis connection
            from src.data.phase1_data_pipeline_unification import UnifiedDataService
            service = UnifiedDataService()
            
            # Test database connection
            if hasattr(service, 'redis_client'):
                working_features.append("Redis Integration")
                test_results["redis_connection"] = True
            else:
                broken_features.append("Redis Integration")
                test_results["redis_connection"] = False
                
            # Test SOL price (check if hardcoded)
            sol_price = await service.get_price("SOL")
            if sol_price and sol_price.price_usd == 100.0:
                broken_features.append("Hardcoded SOL Price")
                test_results["real_sol_price"] = False
            else:
                working_features.append("Real SOL Price")
                test_results["real_sol_price"] = True
                
            # Test Birdeye URL
            if hasattr(service, 'birdeye_base_url') and service.birdeye_base_url == "UNIFIED_DATA_SERVICE":
                broken_features.append("Invalid Birdeye URL")
                test_results["birdeye_url"] = False
            else:
                working_features.append("Valid Birdeye URL")
                test_results["birdeye_url"] = True
                
        except Exception as e:
            self.logger.error(f"Data pipeline validation failed: {e}")
            broken_features.append(f"Validation Error: {str(e)}")
            
        # Calculate functionality level
        total_tests = len(test_results)
        passed_tests = sum(test_results.values())
        percentage = (passed_tests / total_tests * 100) if total_tests > 0 else 0
        
        if percentage >= 95:
            level = FunctionalityLevel.FULLY_FUNCTIONAL
        elif percentage >= 70:
            level = FunctionalityLevel.MOSTLY_FUNCTIONAL
        elif percentage >= 40:
            level = FunctionalityLevel.PARTIALLY_FUNCTIONAL
        elif percentage >= 10:
            level = FunctionalityLevel.MINIMAL_FUNCTIONAL
        else:
            level = FunctionalityLevel.NON_FUNCTIONAL
            
        return ComponentStatus(
            name="Data Pipeline",
            file_path="src/data/phase1_data_pipeline_unification.py",
            lines_of_code=830,
            functionality_level=level,
            working_features=working_features,
            broken_features=broken_features,
            test_results=test_results,
            last_validated=datetime.now()
        )
    
    async def validate_alpha_agent(self) -> ComponentStatus:
        """Validate ALPHA Agent functionality"""
        working_features = []
        broken_features = []
        test_results = {}
        
        try:
            from src.agents.alpha_agent import ALPHAAgent
            agent = ALPHAAgent()
            
            # Test signal generation
            signal = await agent.generate_signal("SOL")
            if signal:
                working_features.append("Signal Generation")
                test_results["signal_generation"] = True
            else:
                broken_features.append("Signal Generation")
                test_results["signal_generation"] = False
                
            # Test momentum calculation
            if hasattr(agent, 'calculate_momentum'):
                working_features.append("Momentum Calculation")
                test_results["momentum_calculation"] = True
            else:
                broken_features.append("Momentum Calculation")
                test_results["momentum_calculation"] = False
                
        except Exception as e:
            self.logger.error(f"ALPHA Agent validation failed: {e}")
            broken_features.append(f"Validation Error: {str(e)}")
            
        # Calculate functionality level
        total_tests = len(test_results)
        passed_tests = sum(test_results.values())
        percentage = (passed_tests / total_tests * 100) if total_tests > 0 else 0
        
        if percentage >= 95:
            level = FunctionalityLevel.FULLY_FUNCTIONAL
        elif percentage >= 70:
            level = FunctionalityLevel.MOSTLY_FUNCTIONAL
        elif percentage >= 40:
            level = FunctionalityLevel.PARTIALLY_FUNCTIONAL
        elif percentage >= 10:
            level = FunctionalityLevel.MINIMAL_FUNCTIONAL
        else:
            level = FunctionalityLevel.NON_FUNCTIONAL
            
        return ComponentStatus(
            name="ALPHA Agent",
            file_path="src/agents/alpha_agent.py",
            lines_of_code=352,
            functionality_level=level,
            working_features=working_features,
            broken_features=broken_features,
            test_results=test_results,
            last_validated=datetime.now()
        )
    
    async def validate_execution_engine(self) -> ComponentStatus:
        """Validate execution engine functionality"""
        working_features = []
        broken_features = []
        test_results = {}
        
        try:
            from src.core.execution.unified_execution_engine import UnifiedExecutionEngine
            engine = UnifiedExecutionEngine()
            
            # Test subprocess execution capability
            if hasattr(engine, 'execute_trade'):
                working_features.append("Trade Execution Interface")
                test_results["execution_interface"] = True
            else:
                broken_features.append("Trade Execution Interface")
                test_results["execution_interface"] = False
                
            # Check for missing CLI files
            import os
            cli_files = ["execute_buy.js", "execute_sell.js"]
            missing_files = [f for f in cli_files if not os.path.exists(f)]
            
            if missing_files:
                broken_features.append(f"Missing CLI Files: {missing_files}")
                test_results["cli_files"] = False
            else:
                working_features.append("CLI Files Present")
                test_results["cli_files"] = True
                
        except Exception as e:
            self.logger.error(f"Execution engine validation failed: {e}")
            broken_features.append(f"Validation Error: {str(e)}")
            
        # Calculate functionality level
        total_tests = len(test_results)
        passed_tests = sum(test_results.values())
        percentage = (passed_tests / total_tests * 100) if total_tests > 0 else 0
        
        if percentage >= 95:
            level = FunctionalityLevel.FULLY_FUNCTIONAL
        elif percentage >= 70:
            level = FunctionalityLevel.MOSTLY_FUNCTIONAL
        elif percentage >= 40:
            level = FunctionalityLevel.PARTIALLY_FUNCTIONAL
        elif percentage >= 10:
            level = FunctionalityLevel.MINIMAL_FUNCTIONAL
        else:
            level = FunctionalityLevel.NON_FUNCTIONAL
            
        return ComponentStatus(
            name="Execution Engine",
            file_path="src/core/execution/unified_execution_engine.py",
            lines_of_code=291,
            functionality_level=level,
            working_features=working_features,
            broken_features=broken_features,
            test_results=test_results,
            last_validated=datetime.now()
        )
    
    async def run_full_validation(self) -> Dict[str, ComponentStatus]:
        """Run complete system validation"""
        self.logger.info("Starting full system validation...")
        
        results = {}
        
        # Validate all components
        results["data_pipeline"] = await self.validate_data_pipeline()
        results["alpha_agent"] = await self.validate_alpha_agent()
        results["execution_engine"] = await self.validate_execution_engine()
        
        # Generate summary report
        self.generate_validation_report(results)
        
        return results
    
    def generate_validation_report(self, results: Dict[str, ComponentStatus]):
        """Generate validation report"""
        report = {
            "validation_timestamp": datetime.now().isoformat(),
            "overall_status": self.calculate_overall_status(results),
            "components": {}
        }
        
        for name, status in results.items():
            report["components"][name] = {
                "functionality_level": status.functionality_level.value,
                "working_features": status.working_features,
                "broken_features": status.broken_features,
                "test_results": status.test_results,
                "lines_of_code": status.lines_of_code
            }
        
        # Save report
        with open("validation_report.json", "w") as f:
            json.dump(report, f, indent=2)
            
        self.logger.info(f"Validation report saved to validation_report.json")
        
        # Print summary
        print("\n" + "="*60)
        print("SYSTEM VALIDATION REPORT")
        print("="*60)
        for name, status in results.items():
            print(f"{status.functionality_level.value} {name}")
            print(f"  ✅ Working: {', '.join(status.working_features)}")
            if status.broken_features:
                print(f"  ❌ Broken: {', '.join(status.broken_features)}")
            print()
    
    def calculate_overall_status(self, results: Dict[str, ComponentStatus]) -> str:
        """Calculate overall system functionality"""
        levels = [status.functionality_level for status in results.values()]
        
        # Count functionality levels
        fully_functional = sum(1 for level in levels if level == FunctionalityLevel.FULLY_FUNCTIONAL)
        mostly_functional = sum(1 for level in levels if level == FunctionalityLevel.MOSTLY_FUNCTIONAL)
        
        total = len(levels)
        functional_percentage = ((fully_functional + mostly_functional) / total * 100) if total > 0 else 0
        
        if functional_percentage >= 80:
            return "🟢 SYSTEM MOSTLY FUNCTIONAL"
        elif functional_percentage >= 60:
            return "🟡 SYSTEM PARTIALLY FUNCTIONAL"
        else:
            return "🔴 SYSTEM NEEDS MAJOR FIXES"

if __name__ == "__main__":
    async def main():
        validator = SystemValidator()
        await validator.run_full_validation()
    
    asyncio.run(main())
